/**
 * 增强版工具栏管理器 - 集成ButtonConfig、PanelManager、EventBus
 * 提供配置驱动的工具栏管理解决方案
 */
class EnhancedToolbarManager {
    constructor(viewer, containerId = 'toolButtons') {
        this.viewer = viewer;
        this.containerId = containerId;
        this.container = null;
        
        // 核心管理器实例
        this.buttonConfig = window.ButtonConfig;
        this.buttonFactory = new ButtonFactory();
        this.panelManager = new PanelManager(window.EventBus);
        this.eventBus = window.EventBus;
        
        // 工具和面板映射
        this.registeredTools = new Map();
        this.toolPanels = new Map();
        this.activeTool = null;
        
        // 快捷键管理
        this.shortcuts = new Map();
        
        // 初始化
        this._initContainer();
        this._setupEventListeners();
        this._setupShortcuts();
        
        console.log('✓ EnhancedToolbarManager 增强版工具栏管理器初始化完成');
    }

    /**
     * 初始化容器
     */
    _initContainer() {
        this.container = document.getElementById(this.containerId);
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = this.containerId;
            this.container.className = 'enhanced-toolbar-container';
            document.body.appendChild(this.container);
        }
    }

    /**
     * 设置事件监听器
     */
    _setupEventListeners() {
        // 监听面板事件
        this.eventBus.on(window.EVENTS.PANEL.ACTION, (eventData) => {
            this._handlePanelAction(eventData);
        });

        this.eventBus.on(window.EVENTS.PANEL.INPUT, (eventData) => {
            this._handlePanelInput(eventData);
        });

        // 监听系统事件
        this.eventBus.on(window.EVENTS.SYSTEM.CONFIG_CHANGED, (eventData) => {
            this._handleConfigChange(eventData);
        });

        // ESC键关闭所有工具
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.deactivateAllTools();
            }
        });
    }

    /**
     * 设置快捷键
     */
    _setupShortcuts() {
        document.addEventListener('keydown', (event) => {
            const shortcut = this._getShortcutString(event);
            const buttonConfig = this.buttonConfig.findByShortcut(shortcut);
            
            if (buttonConfig && buttonConfig.enabled) {
                event.preventDefault();
                this.toggleTool(buttonConfig.id);
            }
        });
    }

    /**
     * 获取快捷键字符串
     */
    _getShortcutString(event) {
        const keys = [];
        if (event.ctrlKey) keys.push('Ctrl');
        if (event.shiftKey) keys.push('Shift');
        if (event.altKey) keys.push('Alt');
        if (event.key !== 'Control' && event.key !== 'Shift' && event.key !== 'Alt') {
            keys.push(event.key);
        }
        return keys.join('+');
    }

    /**
     * 从配置初始化所有工具
     */
    async initializeFromConfig() {
        const configs = this.buttonConfig.getConfigsByOrder();
        const initPromises = [];

        for (const config of configs) {
            if (config.enabled) {
                const promise = this._initializeToolFromConfig(config);
                initPromises.push(promise);
            }
        }

        const results = await Promise.allSettled(initPromises);
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`✓ 工具初始化完成: 成功 ${successful} 个, 失败 ${failed} 个`);

        if (failed > 0) {
            console.warn('部分工具初始化失败:', results
                .filter(r => r.status === 'rejected')
                .map(r => r.reason));
        }

        // 触发系统就绪事件
        this.eventBus.emit(window.EVENTS.SYSTEM.READY, {
            totalTools: configs.length,
            successful,
            failed
        });

        return { successful, failed };
    }

    /**
     * 从配置初始化单个工具
     */
    async _initializeToolFromConfig(config) {
        try {
            // 创建按钮
            const button = this.buttonFactory.createButton({
                id: config.id,
                iconPath: config.icon,
                tooltip: config.tooltip,
                container: this.container,
                onClick: (event, buttonElement, isActive) => {
                    this._handleToolClick(config.id, isActive, event);
                }
            });

            // 创建面板（如果配置了面板）
            let panelId = null;
            if (config.panel) {
                panelId = this.panelManager.createPanel({
                    id: `${config.id}_panel`,
                    title: config.panel.title || config.name,
                    width: config.panel.width,
                    height: config.panel.height,
                    template: config.panel.template || 'default-panel'
                });

                this.toolPanels.set(config.id, panelId);
            }

            // 动态加载工具实例
            const toolInstance = await this._createToolInstance(config);

            // 注册工具
            this.registeredTools.set(config.id, {
                config,
                instance: toolInstance,
                button,
                panelId,
                isActive: false
            });

            // 触发工具注册事件
            this.eventBus.emit(window.EVENTS.TOOLBAR.TOOL_REGISTERED, {
                toolId: config.id,
                config,
                instance: toolInstance
            });

            console.log(`✓ 工具注册成功: ${config.id}`);
            return { toolId: config.id, success: true };

        } catch (error) {
            console.error(`工具注册失败 (${config.id}):`, error);
            throw error;
        }
    }

    /**
     * 创建工具实例
     */
    async _createToolInstance(config) {
        // 根据工具ID动态创建实例
        try {
            switch (config.id) {
                case 'measure':
                    if (window.MeasureTool) {
                        return new window.MeasureTool({ viewer: this.viewer });
                    }
                    break;
                case 'search':
                    if (window.LocationSearch) {
                        return new window.LocationSearch(this.viewer);
                    }
                    break;
                case 'terrain-dig':
                    if (window.TerrainDigHandler) {
                        return new window.TerrainDigHandler(this.viewer);
                    }
                    break;
                case 'profile-analysis':
                    if (window.ProfileAnalysis) {
                        return new window.ProfileAnalysis(this.viewer);
                    }
                    break;
                case 'bookmark':
                    if (window.BookmarkTool) {
                        return new window.BookmarkTool(this.viewer);
                    }
                    break;
                default:
                    // 尝试通过约定的命名找到工具类
                    const className = this._getToolClassName(config.id);
                    if (window[className]) {
                        return new window[className](this.viewer);
                    }
            }

            console.warn(`工具类未找到: ${config.id}，创建占位符实例`);
            // 创建一个简单的占位符工具实例
            return this._createPlaceholderTool(config);
            
        } catch (error) {
            console.error(`创建工具实例失败 (${config.id}):`, error);
            return null;
        }
    }

    /**
     * 创建占位符工具实例
     */
    _createPlaceholderTool(config) {
        return {
            id: config.id,
            name: config.name,
            isPlaceholder: true,
            activate: () => {
                console.log(`激活工具: ${config.name}`);
                // 发送面板显示事件
                this.eventBus.emit(window.EVENTS.PANEL.SHOWN, {
                    panelId: `${config.id}_panel`,
                    toolId: config.id
                });
            },
            deactivate: () => {
                console.log(`停用工具: ${config.name}`);
                // 发送面板关闭事件
                this.eventBus.emit(window.EVENTS.PANEL.CLOSED, {
                    panelId: `${config.id}_panel`,
                    toolId: config.id
                });
            },
            isActive: () => false,
            destroy: () => {
                console.log(`销毁工具: ${config.name}`);
            }
        };
    }

    /**
     * 获取工具类名称
     */
    _getToolClassName(toolId) {
        // 将 kebab-case 转换为 PascalCase
        return toolId
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('') + 'Tool';
    }

    /**
     * 处理工具点击
     */
    _handleToolClick(toolId, wasActive, event) {
        try {
            this.eventBus.emit(window.EVENTS.TOOLBAR.BUTTON_CLICKED, {
                toolId,
                wasActive,
                event
            });

            if (wasActive) {
                this.deactivateTool(toolId);
            } else {
                this.activateTool(toolId);
            }
        } catch (error) {
            console.error(`工具点击处理错误 (${toolId}):`, error);
            this.eventBus.emit(window.EVENTS.SYSTEM.ERROR, {
                source: 'toolbar',
                toolId,
                error: error.message
            });
        }
    }

    /**
     * 激活工具
     */
    async activateTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData) {
            console.warn(`工具未找到: ${toolId}`);
            return;
        }

        try {
            // 停用当前激活的工具
            if (this.activeTool && this.activeTool !== toolId) {
                await this.deactivateTool(this.activeTool);
            }

            // 激活工具实例
            if (toolData.instance && typeof toolData.instance.activate === 'function') {
                await toolData.instance.activate();
            }

            // 显示面板
            if (toolData.panelId) {
                this.panelManager.showPanel(toolData.panelId, toolData.button);
            }

            // 更新状态
            toolData.isActive = true;
            this.activeTool = toolId;
            this.buttonFactory.setButtonState(toolId, true);

            // 触发激活事件
            this.eventBus.emit(window.EVENTS.TOOLBAR.BUTTON_ACTIVATED, {
                toolId,
                instance: toolData.instance,
                panelId: toolData.panelId
            });

            console.log(`✓ 工具激活: ${toolId}`);

        } catch (error) {
            console.error(`工具激活失败 (${toolId}):`, error);
            this.eventBus.emit(window.EVENTS.SYSTEM.ERROR, {
                source: 'toolbar',
                action: 'activate',
                toolId,
                error: error.message
            });
        }
    }

    /**
     * 停用工具
     */
    async deactivateTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData || !toolData.isActive) return;

        try {
            // 停用工具实例
            if (toolData.instance && typeof toolData.instance.deactivate === 'function') {
                await toolData.instance.deactivate();
            }

            // 关闭面板
            if (toolData.panelId) {
                this.panelManager.closePanel(toolData.panelId);
            }

            // 更新状态
            toolData.isActive = false;
            if (this.activeTool === toolId) {
                this.activeTool = null;
            }
            this.buttonFactory.setButtonState(toolId, false);

            // 触发停用事件
            this.eventBus.emit(window.EVENTS.TOOLBAR.BUTTON_DEACTIVATED, {
                toolId,
                instance: toolData.instance
            });

            console.log(`✓ 工具停用: ${toolId}`);

        } catch (error) {
            console.error(`工具停用失败 (${toolId}):`, error);
            this.eventBus.emit(window.EVENTS.SYSTEM.ERROR, {
                source: 'toolbar',
                action: 'deactivate',
                toolId,
                error: error.message
            });
        }
    }

    /**
     * 切换工具状态
     */
    async toggleTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData) return;

        if (toolData.isActive) {
            await this.deactivateTool(toolId);
        } else {
            await this.activateTool(toolId);
        }
    }

    /**
     * 停用所有工具
     */
    async deactivateAllTools() {
        const activeTools = Array.from(this.registeredTools.entries())
            .filter(([_, toolData]) => toolData.isActive)
            .map(([toolId, _]) => toolId);

        const promises = activeTools.map(toolId => this.deactivateTool(toolId));
        await Promise.all(promises);

        console.log('✓ 所有工具已停用');
    }

    /**
     * 处理面板动作
     */
    _handlePanelAction(eventData) {
        const { panelId, action, target } = eventData;
        
        // 查找对应的工具
        const toolId = Array.from(this.toolPanels.entries())
            .find(([_, pid]) => pid === panelId)?.[0];

        if (!toolId) return;

        const toolData = this.registeredTools.get(toolId);
        if (!toolData || !toolData.instance) return;

        // 触发工具特定事件
        const toolEventName = `tool:${toolId}:${action}`;
        this.eventBus.emit(toolEventName, {
            toolId,
            action,
            target,
            instance: toolData.instance
        });

        // 调用工具实例的处理方法
        const methodName = `handle${action.charAt(0).toUpperCase() + action.slice(1)}`;
        if (typeof toolData.instance[methodName] === 'function') {
            toolData.instance[methodName](eventData);
        }
    }

    /**
     * 处理面板输入
     */
    _handlePanelInput(eventData) {
        const { panelId, field, value } = eventData;
        
        // 查找对应的工具
        const toolId = Array.from(this.toolPanels.entries())
            .find(([_, pid]) => pid === panelId)?.[0];

        if (!toolId) return;

        // 触发输入事件
        this.eventBus.emit(`tool:${toolId}:input`, {
            toolId,
            field,
            value,
            panelId
        });
    }

    /**
     * 处理配置变化
     */
    _handleConfigChange(eventData) {
        console.log('配置变化，重新初始化工具栏...');
        // 这里可以实现配置热更新逻辑
    }

    /**
     * 动态添加工具
     */
    async addTool(toolConfig, toolClass = null) {
        // 添加到配置系统
        this.buttonConfig.addConfig(toolConfig.id, toolConfig);
        
        // 如果提供了工具类，直接注册
        if (toolClass) {
            window[this._getToolClassName(toolConfig.id)] = toolClass;
        }

        // 初始化工具
        await this._initializeToolFromConfig(toolConfig);
        
        console.log(`✓ 动态添加工具: ${toolConfig.id}`);
    }

    /**
     * 移除工具
     */
    removeTool(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData) return;

        // 停用工具
        if (toolData.isActive) {
            this.deactivateTool(toolId);
        }

        // 销毁面板
        if (toolData.panelId) {
            this.panelManager.destroyPanel(toolData.panelId);
            this.toolPanels.delete(toolId);
        }

        // 销毁工具实例
        if (toolData.instance && typeof toolData.instance.destroy === 'function') {
            toolData.instance.destroy();
        }

        // 移除按钮
        this.buttonFactory.removeButton(toolId);

        // 清理注册
        this.registeredTools.delete(toolId);

        // 从配置中移除
        this.buttonConfig.removeConfig(toolId);

        // 触发注销事件
        this.eventBus.emit(window.EVENTS.TOOLBAR.TOOL_UNREGISTERED, { toolId });

        console.log(`✓ 工具移除成功: ${toolId}`);
    }

    /**
     * 获取工具状态
     */
    getToolState(toolId) {
        const toolData = this.registeredTools.get(toolId);
        if (!toolData) return null;

        return {
            id: toolId,
            name: toolData.config.name,
            isActive: toolData.isActive,
            hasPanel: !!toolData.panelId,
            config: toolData.config
        };
    }

    /**
     * 获取所有工具状态
     */
    getAllToolStates() {
        const states = {};
        this.registeredTools.forEach((toolData, toolId) => {
            states[toolId] = {
                name: toolData.config.name,
                category: toolData.config.category,
                isActive: toolData.isActive,
                hasPanel: !!toolData.panelId,
                enabled: toolData.config.enabled
            };
        });
        return states;
    }

    /**
     * 设置主题
     */
    setTheme(themeName) {
        const themeConfig = this.buttonConfig.getThemeConfig(themeName);
        
        // 应用主题到容器
        const style = document.createElement('style');
        style.id = 'enhanced-toolbar-theme';
        
        // 移除旧的主题样式
        const oldStyle = document.getElementById('enhanced-toolbar-theme');
        if (oldStyle) {
            oldStyle.remove();
        }

        style.textContent = `
            .enhanced-toolbar-container .tool-button {
                width: ${themeConfig.buttonSize}px;
                height: ${themeConfig.buttonSize}px;
                margin-bottom: ${themeConfig.buttonSpacing}px;
                border-radius: ${themeConfig.borderRadius};
                background-color: ${themeConfig.backgroundColor};
            }
            .enhanced-toolbar-container .tool-button:hover {
                background-color: ${themeConfig.hoverColor};
            }
            .enhanced-toolbar-container .tool-button.active {
                background-color: ${themeConfig.activeColor};
            }
        `;

        document.head.appendChild(style);

        // 触发主题变化事件
        this.eventBus.emit(window.EVENTS.SYSTEM.THEME_CHANGED, {
            theme: themeName,
            config: themeConfig
        });

        console.log(`✓ 主题已切换: ${themeName}`);
    }

    /**
     * 销毁管理器
     */
    destroy() {
        // 停用所有工具
        this.deactivateAllTools();

        // 销毁所有工具
        this.registeredTools.forEach((_, toolId) => {
            this.removeTool(toolId);
        });

        // 销毁面板管理器
        this.panelManager.closeAllPanels();

        // 销毁按钮工厂
        this.buttonFactory.destroyAll();

        // 清理容器
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        console.log('✓ EnhancedToolbarManager 已销毁');
    }
}

// 将增强版工具栏管理器添加到全局作用域
window.EnhancedToolbarManager = EnhancedToolbarManager;