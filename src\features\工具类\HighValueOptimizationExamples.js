/**
 * 高价值优化系统 - 使用示例和演示代码
 * 展示如何使用ButtonConfig、PanelManager、EventBus、EnhancedToolbarManager
 */

// ==================== 基础使用示例 ====================

/**
 * 示例1: 基于配置的工具栏初始化
 */
async function initializeEnhancedToolbar(viewer) {
    try {
        console.log('🚀 开始初始化增强版工具栏...');

        // 创建增强版工具栏管理器
        const toolbarManager = new EnhancedToolbarManager(viewer, 'toolButtons');
        
        // 从配置自动初始化所有工具
        const result = await toolbarManager.initializeFromConfig();
        
        console.log(`✅ 工具栏初始化完成! 成功: ${result.successful}, 失败: ${result.failed}`);
        
        // 设置为全局变量
        window.enhancedToolbarManager = toolbarManager;
        
        return toolbarManager;
        
    } catch (error) {
        console.error('❌ 工具栏初始化失败:', error);
        throw error;
    }
}

/**
 * 示例2: 动态添加自定义工具
 */
function addCustomTool() {
    // 自定义工具类
    class CustomAnalysisTool {
        constructor(viewer) {
            this.viewer = viewer;
            this.isActive = false;
        }

        async activate() {
            console.log('🔧 自定义分析工具激活');
            this.isActive = true;
            
            // 触发自定义事件
            $emit('tool:custom:activated', { 
                toolName: 'Custom Analysis',
                timestamp: Date.now()
            });
        }

        async deactivate() {
            console.log('🔧 自定义分析工具停用');
            this.isActive = false;
            
            $emit('tool:custom:deactivated', {
                toolName: 'Custom Analysis'
            });
        }

        handleAnalyze(eventData) {
            console.log('🔍 执行自定义分析:', eventData);
            // 自定义分析逻辑
        }

        destroy() {
            console.log('🗑️ 自定义工具销毁');
        }
    }

    // 工具配置
    const customToolConfig = {
        id: 'custom-analysis',
        name: '自定义分析',
        tooltip: '执行自定义地理分析',
        category: 'analysis',
        icon: 'src/features/按钮/测量工具/svg/measure.svg', // 复用现有图标
        shortcuts: ['Ctrl+Alt+A'],
        panel: {
            title: '自定义分析工具',
            width: 300,
            height: 400,
            template: 'form-panel'
        },
        features: ['custom-analyze', 'export-results']
    };

    // 添加到工具栏
    if (window.enhancedToolbarManager) {
        window.enhancedToolbarManager.addTool(customToolConfig, CustomAnalysisTool);
        console.log('✅ 自定义工具已添加');
    }
}

/**
 * 示例3: 事件总线使用演示
 */
function demonstrateEventBus() {
    console.log('🎯 事件总线使用演示');

    // 监听工具激活事件
    const unsubscribe1 = $on(EVENTS.TOOLBAR.BUTTON_ACTIVATED, (eventData) => {
        console.log(`📡 工具激活: ${eventData.toolId}`);
        
        // 可以在这里添加全局的工具激活逻辑
        // 比如统计、日志记录等
    });

    // 监听面板动作事件
    const unsubscribe2 = $on(EVENTS.PANEL.ACTION, (eventData) => {
        console.log(`🎛️ 面板动作: ${eventData.action} in ${eventData.panelId}`);
    });

    // 监听测量工具特定事件
    const unsubscribe3 = $on('tool:measure:start', (eventData) => {
        console.log('📏 开始测量:', eventData);
    });

    // 设置事件过滤器 - 只监听特定工具的事件
    const unsubscribe4 = $on(EVENTS.TOOLBAR.BUTTON_CLICKED, (eventData) => {
        if (eventData.toolId === 'measure') {
            console.log('🎯 测量工具被点击');
        }
    }, {
        filter: (eventData) => eventData.toolId === 'measure'
    });

    // 命名空间使用
    const customNamespace = window.EventBus.namespace('custom');
    customNamespace.on('data:loaded', (eventData) => {
        console.log('📊 自定义数据加载:', eventData);
    });

    // 异步事件发送
    $emit('tool:analyze:start', { 
        type: 'terrain',
        options: { precision: 'high' }
    }, { async: true });

    // 等待特定事件
    window.EventBus.waitFor(EVENTS.SYSTEM.READY, 10000)
        .then(() => console.log('✅ 系统就绪'))
        .catch(() => console.log('⏰ 等待系统就绪超时'));

    // 保存取消监听函数以便后续清理
    window.eventUnsubscribers = [
        unsubscribe1, unsubscribe2, unsubscribe3, unsubscribe4
    ];
}

/**
 * 示例4: 面板管理器使用演示
 */
function demonstratePanelManager() {
    console.log('🗂️ 面板管理器使用演示');

    const panelManager = new PanelManager(window.EventBus);

    // 创建自定义面板模板
    panelManager.registerTemplate('demo-panel', {
        header: true,
        footer: true,
        content: `
            <div class="panel-content">
                <h3>演示面板</h3>
                <div class="form-group">
                    <label>输入参数:</label>
                    <input type="text" data-field="parameter" placeholder="请输入参数">
                </div>
                <div class="form-group">
                    <label>选择选项:</label>
                    <select data-field="option">
                        <option value="option1">选项1</option>
                        <option value="option2">选项2</option>
                        <option value="option3">选项3</option>
                    </select>
                </div>
            </div>
        `,
        footer: `
            <div class="panel-actions">
                <button data-action="execute" class="btn-primary">执行</button>
                <button data-action="reset" class="btn-secondary">重置</button>
                <button data-action="close" class="btn-secondary">关闭</button>
            </div>
        `
    });

    // 创建演示面板
    const demoPanelId = panelManager.createPanel({
        id: 'demo-panel',
        title: '演示面板',
        width: 350,
        height: 'auto',
        template: 'demo-panel',
        draggable: true,
        resizable: true
    });

    // 监听面板事件
    $on(EVENTS.PANEL.ACTION, (eventData) => {
        if (eventData.panelId === demoPanelId) {
            switch (eventData.action) {
                case 'execute':
                    console.log('🎬 执行面板操作');
                    break;
                case 'reset':
                    console.log('🔄 重置面板');
                    break;
            }
        }
    });

    $on(EVENTS.PANEL.INPUT, (eventData) => {
        if (eventData.panelId === demoPanelId) {
            console.log(`📝 面板输入: ${eventData.field} = ${eventData.value}`);
        }
    });

    // 显示面板
    setTimeout(() => {
        panelManager.showPanel(demoPanelId);
    }, 1000);
}

/**
 * 示例5: 配置系统管理演示
 */
function demonstrateConfigManagement() {
    console.log('⚙️ 配置系统管理演示');

    const config = ButtonConfig;

    // 获取所有配置
    const allConfigs = config.getAllConfigs();
    console.log('📋 所有按钮配置:', Object.keys(allConfigs));

    // 按分类获取配置
    const measurementTools = config.getConfigsByCategory('measurement');
    console.log('📏 测量类工具:', Object.keys(measurementTools));

    // 按顺序获取配置
    const orderedConfigs = config.getConfigsByOrder();
    console.log('📊 按顺序排列的工具:', orderedConfigs.map(c => c.name));

    // 动态修改配置
    config.updateConfig('measure', {
        tooltip: '增强型测量工具 - 支持多种测量模式',
        shortcuts: ['Ctrl+M', 'M', 'F1'] // 添加更多快捷键
    });

    // 添加新的分类
    config.categories.experimental = {
        name: '实验功能',
        color: '#795548',
        order: 10
    };

    // 验证配置
    const errors = config.validate();
    if (errors.length === 0) {
        console.log('✅ 配置验证通过');
    } else {
        console.warn('⚠️ 配置验证失败:', errors);
    }

    // 导出配置
    const configJson = config.exportConfig();
    console.log('💾 配置已导出 (长度:', configJson.length, '字符)');
}

/**
 * 示例6: 主题和样式管理
 */
function demonstrateThemeManagement() {
    console.log('🎨 主题管理演示');

    // 切换到紧凑主题
    if (window.enhancedToolbarManager) {
        window.enhancedToolbarManager.setTheme('compact');
        
        // 延时切换到暗色主题
        setTimeout(() => {
            window.enhancedToolbarManager.setTheme('dark');
        }, 3000);

        // 再延时切换回默认主题
        setTimeout(() => {
            window.enhancedToolbarManager.setTheme('default');
        }, 6000);
    }

    // 监听主题变化
    $on(EVENTS.SYSTEM.THEME_CHANGED, (eventData) => {
        console.log(`🎨 主题已切换: ${eventData.theme}`);
    });
}

/**
 * 示例7: 调试和监控
 */
function enableDebugMode() {
    console.log('🔍 启用调试模式');

    // 启用事件总线调试
    if (window.EventBus && typeof window.EventBus.setDebugMode === 'function') {
        window.EventBus.setDebugMode(true);
    } else {
        console.warn('⚠️ EventBus 未正确初始化或 setDebugMode 方法不存在');
    }

    // 监听所有系统错误
    $on(EVENTS.SYSTEM.ERROR, (eventData) => {
        console.error('🚨 系统错误:', eventData);
    });

    // 定期输出统计信息
    setInterval(() => {
        const stats = window.EventBus.getStats();
        console.log('📊 事件总线统计:', stats);
        
        if (window.enhancedToolbarManager) {
            const toolStates = window.enhancedToolbarManager.getAllToolStates();
            const activeTools = Object.entries(toolStates)
                .filter(([_, state]) => state.isActive)
                .map(([id, _]) => id);
            
            if (activeTools.length > 0) {
                console.log('🔧 激活的工具:', activeTools);
            }
        }
    }, 10000); // 每10秒输出一次

    // 输出面板状态
    if (window.enhancedToolbarManager) {
        const panelStates = window.enhancedToolbarManager.panelManager.getAllPanels();
        console.log('🗂️ 面板状态:', panelStates);
    }
}

// ==================== 完整使用示例 ====================

/**
 * 完整的初始化示例 - 替换 index.html 中的初始化代码
 */
async function completeInitializationExample() {
    try {
        console.log('🚀 开始完整的系统初始化...');

        // 1. 初始化 Cesium
        const viewer = initCesium();
        
        // 2. 初始化坐标显示功能（保持现有的）
        window.coordinateDisplay = new CoordinateDisplay(viewer);
        
        // 3. 启用调试模式（开发环境）
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            enableDebugMode();
        }

        // 4. 初始化增强版工具栏
        const toolbarManager = await initializeEnhancedToolbar(viewer);
        
        // 5. 演示各种功能
        demonstrateEventBus();
        demonstratePanelManager();
        demonstrateConfigManagement();
        demonstrateThemeManagement();
        
        // 6. 添加自定义工具演示
        setTimeout(() => {
            addCustomTool();
        }, 2000);

        // 7. 加载 SVG 图标
        fetch('src/images/svg/icons.svg')
            .then(response => response.text())
            .then(svgContent => {
                document.getElementById('svg-container').innerHTML = svgContent;
            })
            .catch(error => console.error('❌ 加载SVG图标失败:', error));

        // 8. 系统就绪通知
        $emit(EVENTS.SYSTEM.READY, {
            version: '2.0.0',
            features: ['ButtonConfig', 'PanelManager', 'EventBus', 'EnhancedToolbarManager'],
            timestamp: new Date().toISOString()
        });

        console.log('✅ 完整系统初始化成功!');
        
        return {
            viewer,
            toolbarManager,
            config: ButtonConfig,
            panelManager: toolbarManager.panelManager,
            eventBus: EventBus
        };
        
    } catch (error) {
        console.error('❌ 系统初始化失败:', error);
        
        // 发送错误事件
        $emit(EVENTS.SYSTEM.ERROR, {
            source: 'initialization',
            error: error.message,
            stack: error.stack
        });
        
        throw error;
    }
}

// ==================== 工具类扩展示例 ====================

/**
 * 创建符合新架构的工具类基类
 */
class BaseTool {
    constructor(viewer, config = {}) {
        this.viewer = viewer;
        this.config = config;
        this.isActive = false;
        this.eventHandlers = [];
    }

    async activate() {
        if (this.isActive) return;
        
        this.isActive = true;
        this._setupEventHandlers();
        
        // 触发激活事件
        $emit(`tool:${this.config.id}:activated`, {
            toolId: this.config.id,
            instance: this
        });
        
        console.log(`✅ 工具激活: ${this.config.id}`);
    }

    async deactivate() {
        if (!this.isActive) return;
        
        this.isActive = false;
        this._cleanupEventHandlers();
        
        // 触发停用事件
        $emit(`tool:${this.config.id}:deactivated`, {
            toolId: this.config.id,
            instance: this
        });
        
        console.log(`🔴 工具停用: ${this.config.id}`);
    }

    _setupEventHandlers() {
        // 子类覆盖此方法设置事件监听
    }

    _cleanupEventHandlers() {
        // 清理所有事件监听器
        this.eventHandlers.forEach(handler => {
            if (typeof handler === 'function') {
                handler(); // 执行取消监听函数
            }
        });
        this.eventHandlers.length = 0;
    }

    destroy() {
        this.deactivate();
        console.log(`🗑️ 工具销毁: ${this.config.id}`);
    }
}

// ==================== 导出示例函数 ====================

// 将示例函数添加到全局作用域，便于在控制台调试
window.ToolbarExamples = {
    initializeEnhancedToolbar,
    addCustomTool,
    demonstrateEventBus,
    demonstratePanelManager,
    demonstrateConfigManagement,
    demonstrateThemeManagement,
    enableDebugMode,
    completeInitializationExample,
    BaseTool
};

console.log('📚 工具栏系统使用示例已加载');
console.log('💡 使用 ToolbarExamples 对象访问所有示例函数');
console.log('🎯 推荐: 将 window.onload 替换为 ToolbarExamples.completeInitializationExample()');

// ==================== 快速开始模板 ====================

/**
 * 在 index.html 中使用的快速开始代码模板:
 * 
 * <script>
 *     window.onload = async function() {
 *         try {
 *             const system = await ToolbarExamples.completeInitializationExample();
 *             console.log('🎉 系统启动成功!', system);
 *         } catch (error) {
 *             console.error('💥 系统启动失败:', error);
 *         }
 *     };
 * </script>
 */