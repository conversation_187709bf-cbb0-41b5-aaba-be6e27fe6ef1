!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).RBush=i()}(this,(function(){"use strict";function t(e,h,r=0,s=e.length-1,a=n){for(;s>r;){if(s-r>600){const i=s-r+1,n=h-r+1,o=Math.log(i),l=.5*Math.exp(2*o/3),c=.5*Math.sqrt(o*l*(i-l)/i)*(n-i/2<0?-1:1);t(e,h,Math.max(r,Math.floor(h-n*l/i+c)),Math.min(s,Math.floor(h+(i-n)*l/i+c)),a)}const n=e[h];let o=r,l=s;for(i(e,r,h),a(e[s],n)>0&&i(e,r,s);o<l;){for(i(e,o,l),o++,l--;a(e[o],n)<0;)o++;for(;a(e[l],n)>0;)l--}0===a(e[r],n)?i(e,r,l):(l++,i(e,l,s)),l<=h&&(r=l+1),h<=l&&(s=l-1)}}function i(t,i,n){const e=t[i];t[i]=t[n],t[n]=e}function n(t,i){return t<i?-1:t>i?1:0}function e(t,i,n){if(!n)return i.indexOf(t);for(let e=0;e<i.length;e++)if(n(t,i[e]))return e;return-1}function h(t,i){r(t,0,t.children.length,i,t)}function r(t,i,n,e,h){h||(h=d(null)),h.minX=1/0,h.minY=1/0,h.maxX=-1/0,h.maxY=-1/0;for(let r=i;r<n;r++){const i=t.children[r];s(h,t.leaf?e(i):i)}return h}function s(t,i){return t.minX=Math.min(t.minX,i.minX),t.minY=Math.min(t.minY,i.minY),t.maxX=Math.max(t.maxX,i.maxX),t.maxY=Math.max(t.maxY,i.maxY),t}function a(t,i){return t.minX-i.minX}function o(t,i){return t.minY-i.minY}function l(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function c(t){return t.maxX-t.minX+(t.maxY-t.minY)}function m(t,i){const n=Math.max(t.minX,i.minX),e=Math.max(t.minY,i.minY),h=Math.min(t.maxX,i.maxX),r=Math.min(t.maxY,i.maxY);return Math.max(0,h-n)*Math.max(0,r-e)}function f(t,i){return t.minX<=i.minX&&t.minY<=i.minY&&i.maxX<=t.maxX&&i.maxY<=t.maxY}function u(t,i){return i.minX<=t.maxX&&i.minY<=t.maxY&&i.maxX>=t.minX&&i.maxY>=t.minY}function d(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function x(i,n,e,h,r){const s=[n,e];for(;s.length;){if((e=s.pop())-(n=s.pop())<=h)continue;const a=n+Math.ceil((e-n)/h/2)*h;t(i,a,n,e,r),s.push(n,a,a,e)}}return class{constructor(t=9){this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()}all(){return this._all(this.data,[])}search(t){let i=this.data;const n=[];if(!u(t,i))return n;const e=this.toBBox,h=[];for(;i;){for(let r=0;r<i.children.length;r++){const s=i.children[r],a=i.leaf?e(s):s;u(t,a)&&(i.leaf?n.push(s):f(t,a)?this._all(s,n):h.push(s))}i=h.pop()}return n}collides(t){let i=this.data;if(!u(t,i))return!1;const n=[];for(;i;){for(let e=0;e<i.children.length;e++){const h=i.children[e],r=i.leaf?this.toBBox(h):h;if(u(t,r)){if(i.leaf||f(t,r))return!0;n.push(h)}}i=n.pop()}return!1}load(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(let i=0;i<t.length;i++)this.insert(t[i]);return this}let i=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===i.height)this._splitRoot(this.data,i);else{if(this.data.height<i.height){const t=this.data;this.data=i,i=t}this._insert(i,this.data.height-i.height-1,!0)}else this.data=i;return this}insert(t){return t&&this._insert(t,this.data.height-1),this}clear(){return this.data=d([]),this}remove(t,i){if(!t)return this;let n=this.data;const h=this.toBBox(t),r=[],s=[];let a,o,l;for(;n||r.length;){if(n||(n=r.pop(),o=r[r.length-1],a=s.pop(),l=!0),n.leaf){const h=e(t,n.children,i);if(-1!==h)return n.children.splice(h,1),r.push(n),this._condense(r),this}l||n.leaf||!f(n,h)?o?(a++,n=o.children[a],l=!1):n=null:(r.push(n),s.push(a),a=0,o=n,n=n.children[0])}return this}toBBox(t){return t}compareMinX(t,i){return t.minX-i.minX}compareMinY(t,i){return t.minY-i.minY}toJSON(){return this.data}fromJSON(t){return this.data=t,this}_all(t,i){const n=[];for(;t;)t.leaf?i.push(...t.children):n.push(...t.children),t=n.pop();return i}_build(t,i,n,e){const r=n-i+1;let s,a=this._maxEntries;if(r<=a)return s=d(t.slice(i,n+1)),h(s,this.toBBox),s;e||(e=Math.ceil(Math.log(r)/Math.log(a)),a=Math.ceil(r/Math.pow(a,e-1))),s=d([]),s.leaf=!1,s.height=e;const o=Math.ceil(r/a),l=o*Math.ceil(Math.sqrt(a));x(t,i,n,l,this.compareMinX);for(let h=i;h<=n;h+=l){const i=Math.min(h+l-1,n);x(t,h,i,o,this.compareMinY);for(let n=h;n<=i;n+=o){const h=Math.min(n+o-1,i);s.children.push(this._build(t,n,h,e-1))}}return h(s,this.toBBox),s}_chooseSubtree(t,i,n,e){for(;e.push(i),!i.leaf&&e.length-1!==n;){let n,e=1/0,s=1/0;for(let a=0;a<i.children.length;a++){const o=i.children[a],c=l(o),m=(h=t,r=o,(Math.max(r.maxX,h.maxX)-Math.min(r.minX,h.minX))*(Math.max(r.maxY,h.maxY)-Math.min(r.minY,h.minY))-c);m<s?(s=m,e=c<e?c:e,n=o):m===s&&c<e&&(e=c,n=o)}i=n||i.children[0]}var h,r;return i}_insert(t,i,n){const e=n?t:this.toBBox(t),h=[],r=this._chooseSubtree(e,this.data,i,h);for(r.children.push(t),s(r,e);i>=0&&h[i].children.length>this._maxEntries;)this._split(h,i),i--;this._adjustParentBBoxes(e,h,i)}_split(t,i){const n=t[i],e=n.children.length,r=this._minEntries;this._chooseSplitAxis(n,r,e);const s=this._chooseSplitIndex(n,r,e),a=d(n.children.splice(s,n.children.length-s));a.height=n.height,a.leaf=n.leaf,h(n,this.toBBox),h(a,this.toBBox),i?t[i-1].children.push(a):this._splitRoot(n,a)}_splitRoot(t,i){this.data=d([t,i]),this.data.height=t.height+1,this.data.leaf=!1,h(this.data,this.toBBox)}_chooseSplitIndex(t,i,n){let e,h=1/0,s=1/0;for(let a=i;a<=n-i;a++){const i=r(t,0,a,this.toBBox),o=r(t,a,n,this.toBBox),c=m(i,o),f=l(i)+l(o);c<h?(h=c,e=a,s=f<s?f:s):c===h&&f<s&&(s=f,e=a)}return e||n-i}_chooseSplitAxis(t,i,n){const e=t.leaf?this.compareMinX:a,h=t.leaf?this.compareMinY:o;this._allDistMargin(t,i,n,e)<this._allDistMargin(t,i,n,h)&&t.children.sort(e)}_allDistMargin(t,i,n,e){t.children.sort(e);const h=this.toBBox,a=r(t,0,i,h),o=r(t,n-i,n,h);let l=c(a)+c(o);for(let e=i;e<n-i;e++){const i=t.children[e];s(a,t.leaf?h(i):i),l+=c(a)}for(let e=n-i-1;e>=i;e--){const i=t.children[e];s(o,t.leaf?h(i):i),l+=c(o)}return l}_adjustParentBBoxes(t,i,n){for(let e=n;e>=0;e--)s(i[e],t)}_condense(t){for(let i,n=t.length-1;n>=0;n--)0===t[n].children.length?n>0?(i=t[n-1].children,i.splice(i.indexOf(t[n]),1)):this.clear():h(t[n],this.toBBox)}}}));
