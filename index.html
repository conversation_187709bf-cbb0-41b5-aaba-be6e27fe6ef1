<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 功能性CSS文件 - 新中文路径 -->
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measureTool.css">
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measure.css">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    <link rel="stylesheet" href="src/features/按钮/搜索功能/styles/search.css">
    <link rel="stylesheet" href="src/features/按钮/地形开挖/styles/terrain-dig.css">
    <link rel="stylesheet" href="src/features/按钮/剖面分析/styles/toolbar-buttons.css">
    <link rel="stylesheet" href="src/features/按钮/书签管理/styles/bookmark.css">
    <!-- 添加roamFly样式 -->
    <link rel="stylesheet" href="src/features/按钮/漫游飞行/styles/roamFly.css">
    <!-- 添加打印功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/打印功能/styles/print.css">
    <!-- 添加标记点功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/标记管理/styles/addMarker.css">
    <!-- 添加场景管理功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/场景管理/styles/sceneManager.css">
    <!-- 添加3D建筑功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/三维建筑/styles/building3d.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 确保工具类先加载 -->
    <script src="src/features/工具类/PanelPositioner.js"></script>
    <!-- 高价值优化系统 -->
    <script src="src/features/工具类/EventBus.js"></script>
    <script src="src/features/工具类/ButtonConfig.js"></script>
    <script src="src/features/工具类/PanelManager.js"></script>
    <script src="src/features/工具类/ButtonFactory.js"></script>
    <script src="src/features/工具类/EnhancedToolbarManager.js"></script>
    <!-- 使用示例和演示 -->
    <script src="src/features/工具类/HighValueOptimizationExamples.js"></script>
    <!-- 原有工具栏管理器（备用） -->
    <script src="src/features/工具类/ToolbarManager.js"></script>
    <script src="src/features/工具类/ToolbarManagerExample.js"></script>
    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <!-- 功能性JS文件 - 新中文路径 -->
    <script src="src/features/按钮/测量工具/core/MeasureTool.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/按钮/搜索功能/core/LocationSearch.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
    <script src="src/features/按钮/地形开挖/core/TerrainDigHandler.js"></script>
    <script src="src/features/按钮/剖面分析/core/ProfileAnalysis.js"></script>
    <script src="src/features/按钮/书签管理/core/BookmarkTool.js"></script>
    
    <!-- UI组件JS文件 -->
    <script src="src/features/按钮/搜索功能/ui/SearchUI.js"></script>
    <script src="src/features/按钮/地形开挖/ui/TerrainDigUI.js"></script>
    <script src="src/features/按钮/剖面分析/ui/ProfileAnalysisUI.js"></script>
    <script src="src/features/按钮/测量工具/ui/MeasureUI.js"></script>
    <script src="src/features/按钮/书签管理/ui/BookmarkUI.js"></script>
    <!-- 添加roamFly模块 -->
    <script type="module" src="src/features/按钮/漫游飞行/index.js"></script>
    <!-- 添加打印功能模块 -->
    <script src="src/features/按钮/打印功能/config.js"></script>
    <script src="src/features/按钮/打印功能/core/PrintTool.js"></script>
    <script src="src/features/按钮/打印功能/ui/PrintUI.js"></script>
    <!-- 添加标记点功能模块 -->
    <script src="src/features/按钮/标记管理/core/AddMarkerTool.js"></script>
    <script src="src/features/按钮/标记管理/ui/AddMarkerUI.js"></script>
    <!-- 添加场景管理功能模块 -->
    <script type="module" src="src/features/按钮/场景管理/index.js"></script>
    <!-- 添加3D建筑功能模块 -->
    <script src="src/features/按钮/三维建筑/config.js"></script>
    <script src="src/features/按钮/三维建筑/core/Building3DTool.js"></script>
    <script src="src/features/按钮/三维建筑/ui/Building3DUI.js"></script>
</head>
<body>
    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <div id="cesiumContainer"></div>
    
    <!-- 工具按钮组 - 按钮将由各UI组件动态添加 -->
    <div id="toolButtons"></div>
    
    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 等待页面加载完成
        window.onload = function() {
            try {
                // 使用优化后的初始化流程
                initializeSystem();
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };

        /**
         * 优化后的系统初始化函数
         * 整合了新的工具类系统，同时保持向后兼容
         */
        async function initializeSystem() {
            console.log('🚀 开始系统初始化...');
            
            // 1. 初始化Cesium
            const viewer = initCesium();
            
            // 2. 初始化坐标显示功能
            window.coordinateDisplay = new CoordinateDisplay(viewer);
            
            // 3. 启用调试模式（开发环境）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                if (typeof ToolbarExamples !== 'undefined' && ToolbarExamples.enableDebugMode) {
                    ToolbarExamples.enableDebugMode();
                }
            }
            
            // 4. 尝试初始化增强版工具栏（如果可用）
            if (typeof ToolbarExamples !== 'undefined' && ToolbarExamples.initializeEnhancedToolbar) {
                try {
                    const toolbarManager = await ToolbarExamples.initializeEnhancedToolbar(viewer);
                    console.log('✅ 增强版工具栏初始化成功');
                } catch (error) {
                    console.warn('⚠️ 增强版工具栏初始化失败，使用传统模式:', error);
                    // 如果增强版失败，回退到传统模式
                    await initializeExistingComponents(viewer);
                }
            } else {
                // 如果增强版不可用，使用传统模式
                await initializeExistingComponents(viewer);
            }

            // 5. 加载SVG图标
            await loadSVGIcons();

            // 6. 系统就绪通知
            if (typeof $emit !== 'undefined') {
                $emit('system:ready', {
                    version: '2.0.0',
                    timestamp: new Date().toISOString()
                });
            }
            
            console.log('✅ 系统初始化完成!');
        }

        /**
         * 初始化现有组件（保持向后兼容）
         */
        async function initializeExistingComponents(viewer) {
            console.log('📦 初始化现有组件...');
            
            // 同步初始化的组件
            window.searchUI = SearchUI.init(viewer, 'toolButtons');
            window.terrainDigUI = TerrainDigUI.init(viewer, 'toolButtons');
            window.profileAnalysisUI = ProfileAnalysisUI.init(viewer, 'toolButtons');
            window.printUI = PrintUI.init(viewer, 'toolButtons');
            window.addMarkerUI = AddMarkerUI.init(viewer, 'toolButtons');
            window.building3DUI = Building3DUI.init(viewer, 'toolButtons');
            
            // 异步初始化的组件
            const promises = [
                MeasureUI.init(viewer, 'toolButtons').then(measureUI => {
                    window.measureUI = measureUI;
                    console.log('✅ 测量工具初始化完成');
                }).catch(error => {
                    console.error('❌ 测量工具初始化失败:', error);
                }),
                
                BookmarkUI.init(viewer, 'toolButtons').then(bookmarkUI => {
                    window.bookmarkUI = bookmarkUI;
                    console.log('✅ 书签管理初始化完成');
                }).catch(error => {
                    console.error('❌ 书签管理初始化失败:', error);
                })
            ];
            
            // 动态导入的组件
            import('./src/features/按钮/漫游飞行/index.js').then(module => {
                window.roamFlyUI = module.RoamFlyUI.init(viewer, 'toolButtons');
                console.log('✅ 漫游飞行功能初始化完成');
            }).catch(error => {
                console.error('❌ 漫游飞行功能初始化失败:', error);
            });
            
            import('./src/features/按钮/场景管理/index.js').then(module => {
                window.sceneManagerUI = module.SceneManagerUI.init(viewer, 'toolButtons');
                console.log('✅ 场景管理功能初始化完成');
            }).catch(error => {
                console.error('❌ 场景管理功能初始化失败:', error);
            });
            
            // 等待所有Promise完成
            await Promise.all(promises);
        }

        /**
         * 加载SVG图标
         */
        async function loadSVGIcons() {
            try {
                const response = await fetch('src/images/svg/icons.svg');
                const svgContent = await response.text();
                document.getElementById('svg-container').innerHTML = svgContent;
                console.log('✅ SVG图标加载完成');
            } catch (error) {
                console.error('❌ 加载SVG图标失败:', error);
            }
        }
    </script>
</body>
</html>
